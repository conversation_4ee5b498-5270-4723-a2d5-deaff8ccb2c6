// Test script for video conversion functionality
const { VideoConverter } = require('./video-converter');
const path = require('path');
const os = require('os');

async function testConversion() {
    console.log('🧪 Testing Video Conversion...\n');
    
    const converter = new VideoConverter();
    
    // Test M3U8 detection
    const testUrls = [
        'https://example.com/video.m3u8',
        'https://example.com/playlist.m3u8',
        'https://example.com/video.mp4',
        'https://example.com/stream.ts'
    ];
    
    console.log('📋 Testing format detection:');
    testUrls.forEach(url => {
        const isConvertible = converter.isConvertibleFormat(url);
        console.log(`   ${url} -> ${isConvertible ? '✅ Convertible' : '❌ Not convertible'}`);
    });
    
    console.log('\n📁 Testing filename generation:');
    const testTitles = [
        'My Video Title',
        'Video with special chars: <>/\\|?*',
        null
    ];
    
    testTitles.forEach(title => {
        const filename = converter.generateFilename('https://example.com/video.m3u8', title);
        console.log(`   "${title}" -> "${filename}"`);
    });
    
    // Set up event listeners
    converter.on('progress', (data) => {
        console.log(`📊 Progress: ${Math.round(data.percent || 0)}% - ${data.currentTime || ''}`);
    });
    
    converter.on('start', (data) => {
        console.log('🚀 Conversion started');
    });
    
    converter.on('complete', (data) => {
        console.log(`✅ Conversion completed: ${data.outputPath}`);
    });
    
    converter.on('error', (data) => {
        console.log(`❌ Conversion error: ${data.error}`);
    });
    
    console.log('\n🔧 Video converter initialized successfully!');
    console.log('💡 To test actual conversion, use a real M3U8 URL in the main application.');
    
    return true;
}

// Test FFmpeg availability
async function testFFmpeg() {
    console.log('🎬 Testing FFmpeg availability...\n');
    
    try {
        const ffmpeg = require('fluent-ffmpeg');
        const ffmpegStatic = require('ffmpeg-static');
        const ffprobeStatic = require('ffprobe-static');
        
        console.log(`✅ FFmpeg path: ${ffmpegStatic}`);
        console.log(`✅ FFprobe path: ${ffprobeStatic.path}`);
        
        // Set paths
        ffmpeg.setFfmpegPath(ffmpegStatic);
        ffmpeg.setFfprobePath(ffprobeStatic.path);
        
        console.log('✅ FFmpeg configured successfully!');
        return true;
    } catch (error) {
        console.error('❌ FFmpeg test failed:', error.message);
        return false;
    }
}

// Run tests
async function runAllTests() {
    console.log('🎯 VideoExtract Conversion Tests\n');
    console.log('=' .repeat(50));
    
    try {
        // Test FFmpeg
        const ffmpegOk = await testFFmpeg();
        if (!ffmpegOk) {
            console.log('\n❌ FFmpeg tests failed. Conversion features may not work.');
            return;
        }
        
        console.log('\n' + '=' .repeat(50));
        
        // Test converter
        const converterOk = await testConversion();
        if (!converterOk) {
            console.log('\n❌ Converter tests failed.');
            return;
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🎉 All tests passed! Conversion features are ready.');
        console.log('\n💡 Tips for using conversion:');
        console.log('   • M3U8 and TS files can be converted to MP4');
        console.log('   • Conversion happens in the background');
        console.log('   • Progress is shown in real-time');
        console.log('   • Files are saved to Downloads/VideoExtract folder');
        console.log('   • You can cancel conversions if needed');
        
    } catch (error) {
        console.error('💥 Test suite crashed:', error);
    }
}

// Run if called directly
if (require.main === module) {
    runAllTests().then(() => {
        console.log('\n🏁 Tests completed!');
        process.exit(0);
    }).catch(error => {
        console.error('💥 Tests crashed:', error);
        process.exit(1);
    });
}

module.exports = { testConversion, testFFmpeg, runAllTests };
