const { chromium } = require('playwright');
const { M3U8Analyzer } = require('./m3u8-analyzer');

// Video file extensions and patterns to look for
const VIDEO_PATTERNS = [
  /\.mp4(\?.*)?$/i,
  /\.webm(\?.*)?$/i,
  /\.avi(\?.*)?$/i,
  /\.mov(\?.*)?$/i,
  /\.mkv(\?.*)?$/i,
  /\.flv(\?.*)?$/i,
  /\.wmv(\?.*)?$/i,
  /\.m4v(\?.*)?$/i,
  /\.3gp(\?.*)?$/i,
  /\.ogv(\?.*)?$/i,
  /\.m3u8(\?.*)?$/i,
  /\.mpd(\?.*)?$/i,
  /\/manifest\.m3u8/i,
  /\/playlist\.m3u8/i,
  /videoplayback\?/i,
  /video\?/i
];

// Streaming patterns
const STREAMING_PATTERNS = [
  /hls/i,
  /dash/i,
  /stream/i,
  /video/i,
  /media/i
];

async function extractVideoUrl(pageUrl, options = {}) {
  const {
    timeout = 30000,
    waitTime = 5000,
    headless = true,
    userAgent = null,
    viewport = { width: 1920, height: 1080 }
  } = options;

  let browser;
  let foundVideos = [];
  let networkRequests = [];

  try {
    // Launch browser
    browser = await chromium.launch({
      headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const context = await browser.newContext({
      userAgent: userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport
    });

    const page = await context.newPage();

    // Set up network monitoring
    page.on('response', async (response) => {
      const url = response.url();
      const contentType = response.headers()['content-type'] || '';

      // Store all network requests for analysis
      networkRequests.push({
        url,
        contentType,
        status: response.status(),
        headers: response.headers()
      });

      // Check for video patterns
      const isVideo = VIDEO_PATTERNS.some(pattern => pattern.test(url)) ||
                     contentType.includes('video/') ||
                     contentType.includes('application/vnd.apple.mpegurl') ||
                     contentType.includes('application/dash+xml');

      if (isVideo && response.status() === 200) {
        foundVideos.push({
          url,
          contentType,
          headers: response.headers(),
          size: response.headers()['content-length'] || 'unknown'
        });
        console.log(`Found video URL: ${url}`);
      }
    });

    // Navigate to page
    console.log(`Navigating to: ${pageUrl}`);
    await page.goto(pageUrl, {
      waitUntil: 'networkidle',
      timeout
    });

    // Wait for page to fully load and videos to start loading
    await page.waitForTimeout(waitTime);

    // Try to find and interact with video elements
    try {
      const videoElements = await page.$$('video');
      for (const video of videoElements) {
        try {
          // Try to play the video to trigger network requests
          await video.evaluate(el => {
            if (el.paused) {
              el.play().catch(() => {});
            }
          });
        } catch (e) {
          // Ignore errors from individual video elements
        }
      }

      // Wait a bit more for video requests
      if (videoElements.length > 0) {
        await page.waitForTimeout(3000);
      }
    } catch (e) {
      console.log('No video elements found or error interacting with them');
    }

    // Try to find video URLs in page source
    const pageContent = await page.content();
    const sourceVideos = extractVideoUrlsFromSource(pageContent);

    // Combine results
    const allVideos = [...foundVideos, ...sourceVideos];

    // Remove duplicates and sort by quality/size
    const uniqueVideos = removeDuplicates(allVideos);
    const sortedVideos = sortVideosByQuality(uniqueVideos);

    // Analyze M3U8 files for better information
    const analyzedVideos = await analyzeM3U8Videos(sortedVideos);

    return {
      videos: analyzedVideos,
      totalFound: analyzedVideos.length,
      pageTitle: await page.title(),
      pageUrl,
      networkRequests: networkRequests.length
    };

  } catch (error) {
    console.error('Error extracting video:', error);
    throw new Error(`Failed to extract video: ${error.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

function extractVideoUrlsFromSource(html) {
  const videos = [];
  const patterns = [
    /<video[^>]+src=["']([^"']+)["']/gi,
    /<source[^>]+src=["']([^"']+)["']/gi,
    /["']([^"']*\.(?:mp4|webm|avi|mov|mkv|flv|wmv|m4v|3gp|ogv|m3u8|mpd)[^"']*)["']/gi,
    /url\(["']?([^"')]*\.(?:mp4|webm|avi|mov|mkv|flv|wmv|m4v|3gp|ogv|m3u8|mpd)[^"')]*)/gi
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const url = match[1];
      if (url && url.startsWith('http')) {
        videos.push({
          url,
          contentType: 'unknown',
          source: 'page-source',
          size: 'unknown'
        });
      }
    }
  });

  return videos;
}

function removeDuplicates(videos) {
  const seen = new Set();
  return videos.filter(video => {
    const key = video.url.split('?')[0]; // Remove query parameters for comparison
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

function sortVideosByQuality(videos) {
  return videos.sort((a, b) => {
    // Prioritize by file extension (mp4 > webm > others)
    const getExtensionPriority = (url) => {
      if (url.includes('.mp4')) return 3;
      if (url.includes('.webm')) return 2;
      if (url.includes('.m3u8')) return 1;
      return 0;
    };

    const aPriority = getExtensionPriority(a.url);
    const bPriority = getExtensionPriority(b.url);

    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }

    // Then by size if available
    const aSize = parseInt(a.size) || 0;
    const bSize = parseInt(b.size) || 0;

    return bSize - aSize;
  });
}

/**
 * Analyze M3U8 videos to get detailed information
 * @param {Array} videos - Array of video objects
 * @returns {Promise<Array>} - Enhanced video objects
 */
async function analyzeM3U8Videos(videos) {
  const analyzer = new M3U8Analyzer();
  const enhancedVideos = [];

  for (const video of videos) {
    const enhancedVideo = { ...video };

    // Check if this is an M3U8 file
    if (video.url.includes('.m3u8') || video.contentType.includes('application/vnd.apple.mpegurl')) {
      try {
        console.log(`🔍 Analyzing M3U8: ${video.url}`);
        const analysis = await analyzer.analyzeM3U8(video.url, {
          headers: video.headers || {}
        });

        // Add analysis results to the video object
        enhancedVideo.m3u8Analysis = analysis;
        enhancedVideo.m3u8Summary = analyzer.getSummary(analysis);

        // Update video properties with better information
        if (analysis.type === 'master' && analysis.mediaPlaylist) {
          enhancedVideo.totalDuration = analysis.mediaPlaylist.totalDuration;
          enhancedVideo.totalSegments = analysis.mediaPlaylist.totalSegments;
          enhancedVideo.estimatedSize = analysis.mediaPlaylist.estimatedSize;
          enhancedVideo.isDownloadable = analysis.mediaPlaylist.downloadable;
          enhancedVideo.bestQuality = analysis.bestQuality?.quality;
          enhancedVideo.recommendedUrl = analysis.recommendedUrl;
        } else if (analysis.type === 'media') {
          enhancedVideo.totalDuration = analysis.totalDuration;
          enhancedVideo.totalSegments = analysis.totalSegments;
          enhancedVideo.estimatedSize = analysis.estimatedSize;
          enhancedVideo.isDownloadable = analysis.downloadable;
          enhancedVideo.isLive = analysis.isLive;
          enhancedVideo.isVOD = analysis.isVOD;
        }

        console.log(`✅ M3U8 analysis complete: ${enhancedVideo.m3u8Summary.type}, ${enhancedVideo.totalSegments || 0} segments`);

      } catch (error) {
        console.warn(`⚠️ Failed to analyze M3U8 ${video.url}:`, error.message);
        // Keep the original video object if analysis fails
        enhancedVideo.m3u8Error = error.message;
      }
    }

    enhancedVideos.push(enhancedVideo);
  }

  return enhancedVideos;
}

module.exports = { extractVideoUrl };
