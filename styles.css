/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.header-content {
    padding: 0 2rem;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.icon {
    font-size: 2rem;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main content */
.main {
    flex: 1;
    padding: 2rem;
}

/* Input section */
.input-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.input-wrapper {
    display: flex;
    gap: 1rem;
    align-items: stretch;
}

.url-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.url-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.extract-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 140px;
    justify-content: center;
}

.extract-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.extract-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Options section */
.options-section {
    margin-top: 1rem;
}

.options-details {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.options-summary {
    background: #f9fafb;
    padding: 1rem;
    cursor: pointer;
    font-weight: 600;
    color: #374151;
    user-select: none;
}

.options-summary:hover {
    background: #f3f4f6;
}

.options-content {
    padding: 1rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.option-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
}

.option-input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    width: 80px;
}

/* Custom checkbox */
.option-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: inline-block;
    position: relative;
    transition: all 0.2s ease;
}

.option-label input[type="checkbox"]:checked + .checkmark {
    background: #4f46e5;
    border-color: #4f46e5;
}

.option-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Status section */
.status-section {
    margin-bottom: 2rem;
}

.status-message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    display: none;
}

.status-message.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
    display: block;
}

.status-message.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #86efac;
    display: block;
}

.status-message.error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fca5a5;
    display: block;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    width: 0%;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Results section */
.results-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
}

.results-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.video-count {
    background: #4f46e5;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.video-list {
    display: grid;
    gap: 1rem;
}

.video-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    background: #fafafa;
}

.video-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 2px 10px rgba(79, 70, 229, 0.1);
    background: white;
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.video-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.video-meta {
    font-size: 0.875rem;
    color: #6b7280;
}

.video-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.video-url {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    word-break: break-all;
    color: #374151;
    margin-top: 0.5rem;
}

/* Conversion features */
.convertible-badge {
    background: #10b981;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.conversion-progress {
    margin-top: 1rem;
    padding: 1rem;
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-text {
    font-weight: 500;
    color: #0369a1;
}

.progress-percent {
    font-weight: 600;
    color: #0369a1;
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background: #e0f2fe;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #0ea5e9, #0284c7);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* No results */
.no-results {
    text-align: center;
    padding: 3rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.no-results h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.no-results p {
    color: #6b7280;
    max-width: 400px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    padding: 1rem 0;
    text-align: center;
}

.footer-text {
    font-size: 0.875rem;
    color: #6b7280;
}

.footer-link {
    color: #4f46e5;
    text-decoration: none;
}

.footer-link:hover {
    text-decoration: underline;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0.25rem;
    border-radius: 4px;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.modal-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
}

.modal-info p {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }

    .main {
        padding: 1rem;
    }

    .input-wrapper {
        flex-direction: column;
    }

    .extract-btn {
        width: 100%;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .video-header {
        flex-direction: column;
        gap: 1rem;
    }

    .video-actions {
        width: 100%;
        justify-content: stretch;
    }

    .btn {
        flex: 1;
        justify-content: center;
    }
}
