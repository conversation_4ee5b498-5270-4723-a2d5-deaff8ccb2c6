// Simple test script to verify video extraction functionality
const { extractVideoUrl } = require('./video-extractor');

async function testExtraction() {
    console.log('🧪 Testing Video Extraction...\n');
    
    // Test with a simple HTML page that has video elements
    const testUrl = 'https://www.w3schools.com/html/html5_video.asp';
    
    try {
        console.log(`📡 Testing URL: ${testUrl}`);
        console.log('⏳ Extracting videos...\n');
        
        const result = await extractVideoUrl(testUrl, {
            headless: true,
            waitTime: 3000,
            timeout: 20000
        });
        
        console.log('✅ Extraction completed!');
        console.log(`📊 Results:`);
        console.log(`   - Page Title: ${result.pageTitle}`);
        console.log(`   - Videos Found: ${result.totalFound}`);
        console.log(`   - Network Requests: ${result.networkRequests}`);
        
        if (result.videos.length > 0) {
            console.log('\n🎬 Found Videos:');
            result.videos.forEach((video, index) => {
                console.log(`   ${index + 1}. ${video.url}`);
                console.log(`      Type: ${video.contentType}`);
                console.log(`      Size: ${video.size}`);
                console.log('');
            });
        } else {
            console.log('\n❌ No videos found. This might be normal for this test URL.');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
if (require.main === module) {
    testExtraction().then(() => {
        console.log('🏁 Test completed!');
        process.exit(0);
    }).catch(error => {
        console.error('💥 Test crashed:', error);
        process.exit(1);
    });
}

module.exports = { testExtraction };
