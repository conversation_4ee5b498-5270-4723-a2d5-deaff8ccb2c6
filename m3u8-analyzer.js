const axios = require('axios');
const { URL } = require('url');

class M3U8Analyzer {
    constructor() {
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    }

    /**
     * Analyze an M3U8 URL to determine its type and content
     * @param {string} m3u8Url - The M3U8 URL to analyze
     * @param {Object} options - Analysis options
     * @returns {Promise<Object>} - Analysis results
     */
    async analyzeM3U8(m3u8Url, options = {}) {
        const { headers = {}, timeout = 10000 } = options;

        try {
            console.log(`🔍 Analyzing M3U8: ${m3u8Url}`);

            // Fetch the M3U8 content
            const response = await axios.get(m3u8Url, {
                headers: {
                    'User-Agent': this.userAgent,
                    ...headers
                },
                timeout
            });

            const content = response.data;
            const baseUrl = this.getBaseUrl(m3u8Url);

            // Parse the M3U8 content
            const parsed = this.parseM3U8Content(content, baseUrl);

            // Determine the type and analyze accordingly
            if (parsed.isMasterPlaylist) {
                return await this.analyzeMasterPlaylist(parsed, m3u8Url, options);
            } else {
                return await this.analyzeMediaPlaylist(parsed, m3u8Url, options);
            }

        } catch (error) {
            console.error('M3U8 analysis failed:', error.message);
            throw new Error(`Failed to analyze M3U8: ${error.message}`);
        }
    }

    /**
     * Parse M3U8 content into structured data
     * @param {string} content - Raw M3U8 content
     * @param {string} baseUrl - Base URL for resolving relative URLs
     * @returns {Object} - Parsed M3U8 data
     */
    parseM3U8Content(content, baseUrl) {
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        
        const result = {
            isMasterPlaylist: false,
            isMediaPlaylist: false,
            variants: [],
            segments: [],
            totalDuration: 0,
            targetDuration: 0,
            isLive: false,
            isVOD: false,
            version: 1
        };

        let currentVariant = null;
        let currentSegmentDuration = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            if (line.startsWith('#EXTM3U')) {
                // M3U8 header
                continue;
            } else if (line.startsWith('#EXT-X-VERSION:')) {
                result.version = parseInt(line.split(':')[1]);
            } else if (line.startsWith('#EXT-X-TARGETDURATION:')) {
                result.targetDuration = parseInt(line.split(':')[1]);
                result.isMediaPlaylist = true;
            } else if (line.startsWith('#EXT-X-STREAM-INF:')) {
                // Master playlist variant
                result.isMasterPlaylist = true;
                currentVariant = this.parseStreamInf(line);
            } else if (line.startsWith('#EXTINF:')) {
                // Media segment
                result.isMediaPlaylist = true;
                currentSegmentDuration = parseFloat(line.split(':')[1].split(',')[0]);
                result.totalDuration += currentSegmentDuration;
            } else if (line.startsWith('#EXT-X-ENDLIST')) {
                result.isVOD = true;
            } else if (line.startsWith('#EXT-X-PLAYLIST-TYPE:')) {
                const type = line.split(':')[1];
                result.isVOD = type === 'VOD';
                result.isLive = type === 'EVENT';
            } else if (!line.startsWith('#')) {
                // URL line
                const url = this.resolveUrl(line, baseUrl);
                
                if (currentVariant) {
                    // This is a variant URL in master playlist
                    currentVariant.url = url;
                    result.variants.push(currentVariant);
                    currentVariant = null;
                } else if (result.isMediaPlaylist) {
                    // This is a segment URL in media playlist
                    result.segments.push({
                        url,
                        duration: currentSegmentDuration
                    });
                    currentSegmentDuration = 0;
                }
            }
        }

        return result;
    }

    /**
     * Analyze a master playlist to find the best quality stream
     * @param {Object} parsed - Parsed M3U8 data
     * @param {string} originalUrl - Original M3U8 URL
     * @param {Object} options - Options
     * @returns {Promise<Object>} - Analysis results
     */
    async analyzeMasterPlaylist(parsed, originalUrl, options) {
        console.log(`📋 Master playlist found with ${parsed.variants.length} variants`);

        // Sort variants by quality (bandwidth)
        const sortedVariants = parsed.variants.sort((a, b) => (b.bandwidth || 0) - (a.bandwidth || 0));

        const analysis = {
            type: 'master',
            originalUrl,
            totalVariants: parsed.variants.length,
            variants: sortedVariants,
            bestQuality: null,
            recommendedUrl: null
        };

        if (sortedVariants.length > 0) {
            const bestVariant = sortedVariants[0];
            analysis.bestQuality = bestVariant;
            analysis.recommendedUrl = bestVariant.url;

            // Analyze the best quality media playlist
            try {
                const mediaAnalysis = await this.analyzeM3U8(bestVariant.url, options);
                analysis.mediaPlaylist = mediaAnalysis;
            } catch (error) {
                console.warn('Failed to analyze best quality variant:', error.message);
            }
        }

        return analysis;
    }

    /**
     * Analyze a media playlist to get segment information
     * @param {Object} parsed - Parsed M3U8 data
     * @param {string} originalUrl - Original M3U8 URL
     * @param {Object} options - Options
     * @returns {Promise<Object>} - Analysis results
     */
    async analyzeMediaPlaylist(parsed, originalUrl, options) {
        console.log(`🎬 Media playlist found with ${parsed.segments.length} segments`);

        const analysis = {
            type: 'media',
            originalUrl,
            totalSegments: parsed.segments.length,
            totalDuration: parsed.totalDuration,
            targetDuration: parsed.targetDuration,
            isLive: parsed.isLive,
            isVOD: parsed.isVOD,
            segments: parsed.segments,
            estimatedSize: this.estimateSize(parsed),
            downloadable: parsed.isVOD && parsed.segments.length > 0
        };

        return analysis;
    }

    /**
     * Parse EXT-X-STREAM-INF line
     * @param {string} line - The stream info line
     * @returns {Object} - Parsed stream info
     */
    parseStreamInf(line) {
        const info = {};
        const params = line.substring('#EXT-X-STREAM-INF:'.length);
        
        // Parse key=value pairs
        const regex = /([A-Z-]+)=([^,]+)/g;
        let match;
        
        while ((match = regex.exec(params)) !== null) {
            const key = match[1].toLowerCase();
            let value = match[2];
            
            // Remove quotes if present
            if (value.startsWith('"') && value.endsWith('"')) {
                value = value.slice(1, -1);
            }
            
            // Convert numeric values
            if (key === 'bandwidth' || key === 'average-bandwidth') {
                info[key] = parseInt(value);
            } else if (key === 'resolution') {
                const [width, height] = value.split('x').map(Number);
                info.resolution = { width, height };
                info.quality = this.getQualityLabel(height);
            } else {
                info[key] = value;
            }
        }
        
        return info;
    }

    /**
     * Get quality label from height
     * @param {number} height - Video height
     * @returns {string} - Quality label
     */
    getQualityLabel(height) {
        if (height >= 2160) return '4K';
        if (height >= 1440) return '1440p';
        if (height >= 1080) return '1080p';
        if (height >= 720) return '720p';
        if (height >= 480) return '480p';
        if (height >= 360) return '360p';
        return `${height}p`;
    }

    /**
     * Resolve relative URL against base URL
     * @param {string} url - URL to resolve
     * @param {string} baseUrl - Base URL
     * @returns {string} - Resolved URL
     */
    resolveUrl(url, baseUrl) {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        
        try {
            return new URL(url, baseUrl).href;
        } catch (error) {
            console.warn('Failed to resolve URL:', url, 'against', baseUrl);
            return url;
        }
    }

    /**
     * Get base URL from a full URL
     * @param {string} url - Full URL
     * @returns {string} - Base URL
     */
    getBaseUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname.substring(0, urlObj.pathname.lastIndexOf('/') + 1)}`;
        } catch (error) {
            return url;
        }
    }

    /**
     * Estimate total size of the video
     * @param {Object} parsed - Parsed M3U8 data
     * @returns {string} - Estimated size
     */
    estimateSize(parsed) {
        if (!parsed.segments || parsed.segments.length === 0) {
            return 'Unknown';
        }

        // Rough estimation: assume 1MB per minute for standard quality
        const durationMinutes = parsed.totalDuration / 60;
        const estimatedMB = Math.round(durationMinutes * 1.5); // 1.5MB per minute average
        
        if (estimatedMB > 1024) {
            return `~${(estimatedMB / 1024).toFixed(1)} GB`;
        } else {
            return `~${estimatedMB} MB`;
        }
    }

    /**
     * Get a summary of the M3U8 analysis for display
     * @param {Object} analysis - Analysis results
     * @returns {Object} - Display summary
     */
    getSummary(analysis) {
        if (analysis.type === 'master') {
            return {
                type: 'Master Playlist',
                variants: analysis.totalVariants,
                bestQuality: analysis.bestQuality?.quality || 'Unknown',
                bandwidth: analysis.bestQuality?.bandwidth || 'Unknown',
                downloadable: analysis.mediaPlaylist?.downloadable || false,
                totalDuration: analysis.mediaPlaylist?.totalDuration || 0,
                totalSegments: analysis.mediaPlaylist?.totalSegments || 0,
                estimatedSize: analysis.mediaPlaylist?.estimatedSize || 'Unknown'
            };
        } else {
            return {
                type: 'Media Playlist',
                downloadable: analysis.downloadable,
                totalDuration: analysis.totalDuration,
                totalSegments: analysis.totalSegments,
                estimatedSize: analysis.estimatedSize,
                isLive: analysis.isLive,
                isVOD: analysis.isVOD
            };
        }
    }
}

module.exports = { M3U8Analyzer };
