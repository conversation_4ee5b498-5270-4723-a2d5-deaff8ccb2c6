{"name": "video-extract", "version": "1.0.0", "description": "Extract direct video URLs from web pages", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "node test-extraction.js", "test-conversion": "node test-conversion.js", "test-m3u8": "node test-m3u8-analyzer.js", "test-m3u8-conversion": "node test-m3u8-conversion.js", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "postinstall": "playwright install chromium", "clean": "rimraf dist node_modules/.cache", "lint": "echo '<PERSON><PERSON> not configured yet'", "pack": "electron-builder --dir"}, "keywords": ["video", "extraction", "url", "electron", "playwright"], "author": "VideoExtract", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"playwright": "^1.40.0", "axios": "^1.6.0", "fluent-ffmpeg": "^2.1.2", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "node-stream-zip": "^1.15.0"}, "build": {"appId": "com.videoextract.app", "productName": "VideoExtract", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/playwright/.local-browsers/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}