// DOM elements
const urlInput = document.getElementById('url-input');
const extractBtn = document.getElementById('extract-btn');
const btnText = document.querySelector('.btn-text');
const btnLoader = document.querySelector('.btn-loader');
const statusMessage = document.getElementById('status-message');
const progressBar = document.getElementById('progress-bar');
const resultsContainer = document.getElementById('results-container');
const noResults = document.getElementById('no-results');
const videoList = document.getElementById('video-list');
const videoCount = document.getElementById('video-count');

// Options
const headlessMode = document.getElementById('headless-mode');
const waitTime = document.getElementById('wait-time');
const timeout = document.getElementById('timeout');

// Modal elements
const videoModal = document.getElementById('video-modal');
const modalVideo = document.getElementById('modal-video');
const modalUrl = document.getElementById('modal-url');
const modalType = document.getElementById('modal-type');
const modalSize = document.getElementById('modal-size');
const modalClose = document.querySelector('.modal-close');
const copyUrlBtn = document.getElementById('copy-url-btn');
const openUrlBtn = document.getElementById('open-url-btn');

// About link
const aboutLink = document.getElementById('about-link');

// State
let currentVideos = [];
let isExtracting = false;
let activeConversions = new Map();

// Event listeners
extractBtn.addEventListener('click', handleExtract);
urlInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        handleExtract();
    }
});

modalClose.addEventListener('click', closeModal);
videoModal.addEventListener('click', (e) => {
    if (e.target === videoModal) {
        closeModal();
    }
});

copyUrlBtn.addEventListener('click', copyCurrentUrl);
openUrlBtn.addEventListener('click', openCurrentUrl);

aboutLink.addEventListener('click', showAbout);

// Set up conversion event listeners
window.electronAPI.onConversionProgress((event, data) => {
    updateConversionProgress(data);
});

window.electronAPI.onConversionComplete((event, data) => {
    handleConversionComplete(data);
});

window.electronAPI.onConversionError((event, data) => {
    handleConversionError(data);
});

// Main extraction function
async function handleExtract() {
    const url = urlInput.value.trim();

    if (!url) {
        showStatus('Please enter a valid URL', 'error');
        return;
    }

    if (!isValidUrl(url)) {
        showStatus('Please enter a valid HTTP/HTTPS URL', 'error');
        return;
    }

    if (isExtracting) {
        return;
    }

    setExtracting(true);
    hideResults();
    showStatus('Extracting videos from the webpage...', 'info');
    showProgress();

    try {
        const options = {
            headless: headlessMode.checked,
            waitTime: parseInt(waitTime.value) * 1000,
            timeout: parseInt(timeout.value) * 1000
        };

        const result = await window.electronAPI.extractVideo(url, options);

        if (result.success) {
            currentVideos = result.data.videos;
            await displayResults(result.data);
            showStatus(`Successfully found ${result.data.totalFound} video(s)`, 'success');
        } else {
            showStatus(`Error: ${result.error}`, 'error');
            showNoResults();
        }
    } catch (error) {
        console.error('Extraction error:', error);
        showStatus(`Unexpected error: ${error.message}`, 'error');
        showNoResults();
    } finally {
        setExtracting(false);
        hideProgress();
    }
}

// UI state management
function setExtracting(extracting) {
    isExtracting = extracting;
    extractBtn.disabled = extracting;

    if (extracting) {
        btnText.style.display = 'none';
        btnLoader.style.display = 'flex';
    } else {
        btnText.style.display = 'block';
        btnLoader.style.display = 'none';
    }
}

function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
}

function showProgress() {
    progressBar.style.display = 'block';
}

function hideProgress() {
    progressBar.style.display = 'none';
}

function hideResults() {
    resultsContainer.style.display = 'none';
    noResults.style.display = 'none';
}

function showNoResults() {
    resultsContainer.style.display = 'none';
    noResults.style.display = 'block';
}

// Results display
async function displayResults(data) {
    if (data.videos.length === 0) {
        showNoResults();
        return;
    }

    videoCount.textContent = `${data.videos.length} video${data.videos.length === 1 ? '' : 's'} found`;
    videoList.innerHTML = '';

    // Create video items asynchronously
    for (let index = 0; index < data.videos.length; index++) {
        const video = data.videos[index];
        const videoItem = await createVideoItem(video, index);
        videoList.appendChild(videoItem);
    }

    resultsContainer.style.display = 'block';
    noResults.style.display = 'none';
}

async function createVideoItem(video, index) {
    const item = document.createElement('div');
    item.className = 'video-item';
    item.id = `video-item-${index}`;

    const fileName = extractFileName(video.url);
    const fileSize = formatFileSize(video.size);
    const contentType = video.contentType || 'unknown';

    // Get enhanced M3U8 information if available
    const m3u8Info = getM3U8DisplayInfo(video);

    // Check if this is a convertible format
    let isConvertible = false;
    try {
        const result = await window.electronAPI.isConvertibleFormat(video.url);
        isConvertible = result.success && result.isConvertible;
    } catch (error) {
        console.error('Error checking convertible format:', error);
    }

    const convertButton = isConvertible ? `
        <button class="btn btn-success btn-small" onclick="convertVideo(${index})">
            🔄 Convert to MP4
        </button>
    ` : '';

    item.innerHTML = `
        <div class="video-header">
            <div class="video-info">
                <h3>${fileName}</h3>
                <div class="video-meta">
                    Type: ${contentType} • Size: ${fileSize}
                    ${m3u8Info.display}
                    ${isConvertible ? ' • <span class="convertible-badge">Convertible</span>' : ''}
                </div>
            </div>
            <div class="video-actions">
                <button class="btn btn-primary btn-small" onclick="copyVideoUrl(${index})">
                    📋 Copy
                </button>
                <button class="btn btn-secondary btn-small" onclick="previewVideo(${index})">
                    👁️ Preview
                </button>
                <button class="btn btn-secondary btn-small" onclick="openVideoUrl(${index})">
                    🔗 Open
                </button>
                ${convertButton}
            </div>
        </div>
        <div class="video-url">${video.url}</div>
        <div class="conversion-progress" id="progress-${index}" style="display: none;">
            <div class="progress-info">
                <span class="progress-text">Converting...</span>
                <span class="progress-percent">0%</span>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 0%"></div>
            </div>
            <button class="btn btn-danger btn-small" onclick="cancelConversion(${index})">
                ❌ Cancel
            </button>
        </div>
    `;

    return item;
}

// Video actions
async function copyVideoUrl(index) {
    const video = currentVideos[index];
    if (video) {
        try {
            await navigator.clipboard.writeText(video.url);
            showStatus('Video URL copied to clipboard!', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            showStatus('Failed to copy URL', 'error');
        }
    }
}

function previewVideo(index) {
    const video = currentVideos[index];
    if (video) {
        showVideoModal(video);
    }
}

async function openVideoUrl(index) {
    const video = currentVideos[index];
    if (video) {
        try {
            await window.electronAPI.openExternal(video.url);
        } catch (error) {
            console.error('Failed to open URL:', error);
            showStatus('Failed to open URL in browser', 'error');
        }
    }
}

// Modal functions
function showVideoModal(video) {
    modalVideo.src = video.url;
    modalUrl.textContent = video.url;
    modalType.textContent = video.contentType || 'unknown';
    modalSize.textContent = formatFileSize(video.size);

    videoModal.style.display = 'flex';

    // Store current video for modal actions
    videoModal.dataset.currentUrl = video.url;
}

function closeModal() {
    videoModal.style.display = 'none';
    modalVideo.src = '';
    modalVideo.pause();
}

async function copyCurrentUrl() {
    const url = videoModal.dataset.currentUrl;
    if (url) {
        try {
            await navigator.clipboard.writeText(url);
            showStatus('Video URL copied to clipboard!', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            showStatus('Failed to copy URL', 'error');
        }
    }
}

async function openCurrentUrl() {
    const url = videoModal.dataset.currentUrl;
    if (url) {
        try {
            await window.electronAPI.openExternal(url);
        } catch (error) {
            console.error('Failed to open URL:', error);
            showStatus('Failed to open URL in browser', 'error');
        }
    }
}

// Utility functions
function isValidUrl(string) {
    try {
        const url = new URL(string);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
        return false;
    }
}

function extractFileName(url) {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const fileName = pathname.split('/').pop();

        if (fileName && fileName.includes('.')) {
            return fileName;
        }

        // If no filename, create one based on the domain
        const domain = urlObj.hostname.replace('www.', '');
        return `video_from_${domain}`;
    } catch (error) {
        return 'video_file';
    }
}

function formatFileSize(size) {
    if (!size || size === 'unknown') {
        return 'Unknown';
    }

    const bytes = parseInt(size);
    if (isNaN(bytes)) {
        return 'Unknown';
    }

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

async function showAbout() {
    const versions = window.versions;
    const message = `VideoExtract v1.0.0

Built with:
• Electron ${versions.electron()}
• Node.js ${versions.node()}
• Chromium ${versions.chrome()}
• Playwright for web automation

This application helps you extract direct video URLs from web pages by monitoring network requests and analyzing page content.

© 2024 VideoExtract`;

    await window.electronAPI.showMessageBox({
        type: 'info',
        title: 'About VideoExtract',
        message: message,
        buttons: ['OK']
    });
}

// Video conversion functions
async function convertVideo(index) {
    const video = currentVideos[index];
    if (!video) return;

    try {
        // Show conversion progress
        const progressElement = document.getElementById(`progress-${index}`);
        if (progressElement) {
            progressElement.style.display = 'block';
        }

        // Use the recommended URL for master playlists, or original URL for media playlists
        const conversionUrl = video.recommendedUrl || video.url;

        // Start conversion
        const options = {
            title: extractFileName(video.url),
            quality: 'high'
        };

        showStatus('Starting video conversion...', 'info');
        const result = await window.electronAPI.convertVideo(conversionUrl, options);

        if (result.success) {
            // Store conversion info
            activeConversions.set(index, {
                video,
                outputPath: result.outputPath
            });
        } else {
            showStatus(`Conversion failed: ${result.error}`, 'error');
            if (progressElement) {
                progressElement.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Conversion error:', error);
        showStatus(`Conversion error: ${error.message}`, 'error');
    }
}

async function cancelConversion(index) {
    try {
        const conversion = activeConversions.get(index);
        if (conversion) {
            await window.electronAPI.cancelConversion(index.toString());
            activeConversions.delete(index);

            const progressElement = document.getElementById(`progress-${index}`);
            if (progressElement) {
                progressElement.style.display = 'none';
            }

            showStatus('Conversion cancelled', 'info');
        }
    } catch (error) {
        console.error('Cancel conversion error:', error);
        showStatus(`Failed to cancel conversion: ${error.message}`, 'error');
    }
}

function updateConversionProgress(data) {
    const index = parseInt(data.conversionId);
    const progressElement = document.getElementById(`progress-${index}`);

    if (progressElement) {
        const percentElement = progressElement.querySelector('.progress-percent');
        const fillElement = progressElement.querySelector('.progress-bar-fill');
        const textElement = progressElement.querySelector('.progress-text');

        if (percentElement && fillElement) {
            const percent = Math.round(data.percent || 0);
            percentElement.textContent = `${percent}%`;
            fillElement.style.width = `${percent}%`;

            if (textElement) {
                textElement.textContent = `Converting... ${data.currentTime || ''}`;
            }
        }
    }
}

function handleConversionComplete(data) {
    const index = parseInt(data.conversionId);
    const progressElement = document.getElementById(`progress-${index}`);

    if (progressElement) {
        progressElement.style.display = 'none';
    }

    showStatus(`Video converted successfully! Saved to: ${data.outputPath}`, 'success');
    activeConversions.delete(index);

    // Show completion notification
    window.electronAPI.showMessageBox({
        type: 'info',
        title: 'Conversion Complete',
        message: `Video has been converted and saved to:\n${data.outputPath}`,
        buttons: ['OK', 'Open Folder']
    }).then(result => {
        if (result.response === 1) {
            // Open folder containing the file
            const folderPath = require('path').dirname(data.outputPath);
            window.electronAPI.openExternal(`file://${folderPath}`);
        }
    });
}

function handleConversionError(data) {
    const index = parseInt(data.conversionId);
    const progressElement = document.getElementById(`progress-${index}`);

    if (progressElement) {
        progressElement.style.display = 'none';
    }

    showStatus(`Conversion failed: ${data.error}`, 'error');
    activeConversions.delete(index);
}

// M3U8 information display helpers
function getM3U8DisplayInfo(video) {
    if (!video.m3u8Summary) {
        return { display: '', hasInfo: false };
    }

    const summary = video.m3u8Summary;
    let displayParts = [];

    if (summary.type === 'Master Playlist') {
        displayParts.push(`📋 Master (${summary.variants} variants)`);
        if (summary.bestQuality && summary.bestQuality !== 'Unknown') {
            displayParts.push(`🎯 Best: ${summary.bestQuality}`);
        }
    } else if (summary.type === 'Media Playlist') {
        if (summary.isLive) {
            displayParts.push('🔴 Live Stream');
        } else if (summary.isVOD) {
            displayParts.push('🎬 Video on Demand');
        }
    }

    if (summary.totalSegments > 0) {
        displayParts.push(`📦 ${summary.totalSegments} segments`);
    }

    if (summary.totalDuration > 0) {
        displayParts.push(`⏱️ ${formatDuration(summary.totalDuration)}`);
    }

    if (summary.estimatedSize && summary.estimatedSize !== 'Unknown') {
        displayParts.push(`💾 ${summary.estimatedSize}`);
    }

    if (!summary.downloadable) {
        displayParts.push('⚠️ Live/Incomplete');
    }

    const display = displayParts.length > 0 ? ' • ' + displayParts.join(' • ') : '';

    return {
        display,
        hasInfo: displayParts.length > 0,
        summary
    };
}

function formatDuration(seconds) {
    if (!seconds || seconds <= 0) return '0s';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    urlInput.focus();
    showStatus('Enter a webpage URL to extract video links', 'info');
});
