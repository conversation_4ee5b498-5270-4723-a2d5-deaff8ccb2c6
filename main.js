const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const os = require('os');
const { extractVideoUrl } = require('./video-extractor');
const { VideoConverter } = require('./video-converter');

let mainWindow;
let videoConverter;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  });

  mainWindow.loadFile('index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open external links in default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Development tools
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(() => {
  createWindow();

  // Initialize video converter
  videoConverter = new VideoConverter();

  // Set up converter event listeners
  videoConverter.on('progress', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('conversion-progress', data);
    }
  });

  videoConverter.on('complete', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('conversion-complete', data);
    }
  });

  videoConverter.on('error', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('conversion-error', data);
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers
ipcMain.handle('extract-video', async (event, url, options = {}) => {
  try {
    const result = await extractVideoUrl(url, options);
    return { success: true, data: result };
  } catch (error) {
    console.error('Video extraction error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('open-external', async (event, url) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  } catch (error) {
    return { canceled: true, error: error.message };
  }
});

ipcMain.handle('show-message-box', async (event, options) => {
  try {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
  } catch (error) {
    return { response: 0, error: error.message };
  }
});

// Video conversion handlers
ipcMain.handle('convert-video', async (event, inputUrl, options = {}) => {
  try {
    if (!videoConverter) {
      throw new Error('Video converter not initialized');
    }

    // Generate output filename
    const downloadsPath = path.join(os.homedir(), 'Downloads', 'VideoExtract');
    const filename = videoConverter.generateFilename(inputUrl, options.title);
    const outputPath = path.join(downloadsPath, filename);

    const result = await videoConverter.convertToMp4(inputUrl, outputPath, options);
    return { success: true, outputPath: result };
  } catch (error) {
    console.error('Video conversion error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-video-info', async (event, inputUrl) => {
  try {
    if (!videoConverter) {
      throw new Error('Video converter not initialized');
    }

    const info = await videoConverter.getVideoInfo(inputUrl);
    return { success: true, info };
  } catch (error) {
    console.error('Get video info error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('cancel-conversion', async (event, conversionId) => {
  try {
    if (!videoConverter) {
      throw new Error('Video converter not initialized');
    }

    videoConverter.cancelConversion(conversionId);
    return { success: true };
  } catch (error) {
    console.error('Cancel conversion error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('is-convertible-format', async (event, url) => {
  try {
    if (!videoConverter) {
      throw new Error('Video converter not initialized');
    }

    const isConvertible = videoConverter.isConvertibleFormat(url);
    return { success: true, isConvertible };
  } catch (error) {
    console.error('Check convertible format error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('choose-download-location', async (event, defaultFilename) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'Save Video As',
      defaultPath: path.join(os.homedir(), 'Downloads', defaultFilename),
      filters: [
        { name: 'MP4 Videos', extensions: ['mp4'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    return result;
  } catch (error) {
    return { canceled: true, error: error.message };
  }
});
