{"name": "node-stream-zip", "version": "1.15.0", "description": "node.js library for reading and extraction of ZIP archives", "keywords": ["zip", "archive", "unzip", "stream"], "homepage": "https://github.com/antelle/node-stream-zip", "author": "An<PERSON><PERSON> <<EMAIL>> (https://github.com/antelle)", "bugs": {"email": "<EMAIL>", "url": "https://github.com/antelle/node-stream-zip/issues"}, "license": "MIT", "files": ["LICENSE", "node_stream_zip.js", "node_stream_zip.d.ts"], "scripts": {"lint": "eslint node_stream_zip.js test/tests.js", "check-types": "tsc node_stream_zip.d.ts", "test": "nodeunit test/tests.js"}, "main": "node_stream_zip.js", "types": "node_stream_zip.d.ts", "repository": {"type": "git", "url": "https://github.com/antelle/node-stream-zip.git"}, "engines": {"node": ">=0.12.0"}, "devDependencies": {"@types/node": "^14.14.6", "eslint": "^7.19.0", "nodeunit": "^0.11.3", "prettier": "^2.2.1"}, "funding": {"type": "github", "url": "https://github.com/sponsors/antelle"}}