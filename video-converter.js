const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');
const ffprobeStatic = require('ffprobe-static');
const path = require('path');
const fs = require('fs');
const { EventEmitter } = require('events');

// Set FFmpeg and FFprobe paths
ffmpeg.setFfmpegPath(ffmpegStatic);
ffmpeg.setFfprobePath(ffprobeStatic.path);

class VideoConverter extends EventEmitter {
    constructor() {
        super();
        this.activeConversions = new Map();
    }

    /**
     * Convert M3U8 or TS files to MP4 with enhanced compatibility
     * @param {string} inputUrl - URL of the M3U8 or TS file
     * @param {string} outputPath - Path where to save the MP4 file
     * @param {Object} options - Conversion options
     * @returns {Promise<string>} - Path to the converted file
     */
    async convertToMp4(inputUrl, outputPath, options = {}) {
        // Use specialized M3U8 conversion if it's an M3U8 file
        if (inputUrl.includes('.m3u8') || options.isM3U8) {
            return this.convertM3U8ToMp4(inputUrl, outputPath, options);
        }

        return this.convertGenericToMp4(inputUrl, outputPath, options);
    }

    /**
     * Specialized M3U8 to MP4 conversion with robust error handling
     * @param {string} inputUrl - URL of the M3U8 file
     * @param {string} outputPath - Path where to save the MP4 file
     * @param {Object} options - Conversion options
     * @returns {Promise<string>} - Path to the converted file
     */
    async convertM3U8ToMp4(inputUrl, outputPath, options = {}) {
        const {
            quality = 'high',
            maxDuration = null,
            startTime = null,
            headers = {},
            userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        } = options;

        return new Promise((resolve, reject) => {
            const conversionId = Date.now().toString();

            try {
                // Ensure output directory exists
                const outputDir = path.dirname(outputPath);
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir, { recursive: true });
                }

                // Create FFmpeg command with M3U8-optimized settings
                let command = ffmpeg(inputUrl);

                // M3U8-specific input options for maximum compatibility
                const inputOptions = [
                    '-user_agent', userAgent,
                    '-headers', Object.entries(headers)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\r\n'),
                    // Protocol and security options
                    '-protocol_whitelist', 'file,http,https,tcp,tls,crypto,hls,applehttp',
                    '-allowed_extensions', 'ALL',
                    // Connection reliability
                    '-reconnect', '1',
                    '-reconnect_streamed', '1',
                    '-reconnect_delay_max', '5',
                    '-reconnect_at_eof', '1',
                    // HLS-specific options
                    '-hls_flags', 'delete_segments',
                    '-hls_start_number_source', 'generic',
                    // Timeout settings
                    '-timeout', '30000000', // 30 seconds in microseconds
                    '-rw_timeout', '30000000'
                ];

                command = command.inputOptions(inputOptions);

                // M3U8-optimized output options
                const outputOptions = [
                    // Copy streams when possible to avoid re-encoding
                    '-c', 'copy',
                    // If re-encoding is needed, use these settings
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    // Container options
                    '-f', 'mp4',
                    '-movflags', '+faststart+frag_keyframe+empty_moov',
                    // Timestamp handling
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts+discardcorrupt',
                    // Buffer settings
                    '-max_muxing_queue_size', '2048',
                    '-max_interleave_delta', '0',
                    // Error recovery
                    '-err_detect', 'ignore_err',
                    '-ignore_unknown'
                ];

                // Add quality settings only if re-encoding is needed
                if (quality === 'high') {
                    outputOptions.push(
                        '-preset', 'medium',
                        '-crf', '23',
                        '-profile:v', 'high',
                        '-level', '4.0'
                    );
                } else if (quality === 'medium') {
                    outputOptions.push(
                        '-preset', 'fast',
                        '-crf', '28',
                        '-profile:v', 'main',
                        '-level', '3.1'
                    );
                } else if (quality === 'low') {
                    outputOptions.push(
                        '-preset', 'ultrafast',
                        '-crf', '32',
                        '-profile:v', 'baseline',
                        '-level', '3.0'
                    );
                }

                command = command.outputOptions(outputOptions);

                // Set start time if specified
                if (startTime) {
                    command = command.seekInput(startTime);
                }

                // Set duration if specified
                if (maxDuration) {
                    command = command.duration(maxDuration);
                }

                // Store conversion info
                this.activeConversions.set(conversionId, {
                    inputUrl,
                    outputPath,
                    startTime: Date.now(),
                    command
                });

                // Set up progress tracking
                command.on('progress', (progress) => {
                    this.emit('progress', {
                        conversionId,
                        percent: progress.percent || 0,
                        currentTime: progress.timemark,
                        targetSize: progress.targetSize,
                        currentKbps: progress.currentKbps
                    });
                });

                // Handle conversion start
                command.on('start', (commandLine) => {
                    console.log('M3U8 FFmpeg command:', commandLine);
                    this.emit('start', { conversionId, commandLine });
                });

                // Handle conversion completion
                command.on('end', () => {
                    this.activeConversions.delete(conversionId);
                    this.emit('complete', { conversionId, outputPath });
                    resolve(outputPath);
                });

                // Handle errors with retry logic
                command.on('error', (err) => {
                    console.error('M3U8 conversion error:', err.message);
                    this.activeConversions.delete(conversionId);

                    // Try fallback conversion if stream copy failed
                    if (err.message.includes('codec') || err.message.includes('stream')) {
                        console.log('Retrying with re-encoding...');
                        this.retryWithReencoding(inputUrl, outputPath, options, conversionId)
                            .then(resolve)
                            .catch(reject);
                    } else {
                        this.emit('error', { conversionId, error: err.message });
                        reject(new Error(`M3U8 conversion failed: ${err.message}`));
                    }
                });

                // Start conversion
                command.save(outputPath);

            } catch (error) {
                reject(new Error(`Failed to start M3U8 conversion: ${error.message}`));
            }
        });
    }

    /**
     * Fallback conversion with re-encoding
     * @param {string} inputUrl - Input URL
     * @param {string} outputPath - Output path
     * @param {Object} options - Options
     * @param {string} conversionId - Conversion ID
     * @returns {Promise<string>} - Output path
     */
    async retryWithReencoding(inputUrl, outputPath, options, conversionId) {
        return new Promise((resolve, reject) => {
            const command = ffmpeg(inputUrl);

            // Simplified input options for retry
            command.inputOptions([
                '-user_agent', options.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                '-protocol_whitelist', 'file,http,https,tcp,tls,crypto',
                '-reconnect', '1'
            ]);

            // Force re-encoding with safe settings
            command.outputOptions([
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-preset', 'fast',
                '-crf', '28',
                '-movflags', '+faststart',
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts'
            ]);

            command.on('end', () => {
                console.log('Retry conversion successful');
                resolve(outputPath);
            });

            command.on('error', (err) => {
                console.error('Retry conversion failed:', err.message);
                reject(new Error(`Retry conversion failed: ${err.message}`));
            });

            command.save(outputPath);
        });
    }

    /**
     * Generic video conversion (non-M3U8)
     * @param {string} inputUrl - URL of the video file
     * @param {string} outputPath - Path where to save the MP4 file
     * @param {Object} options - Conversion options
     * @returns {Promise<string>} - Path to the converted file
     */
    async convertGenericToMp4(inputUrl, outputPath, options = {}) {
        const {
            quality = 'high',
            maxDuration = null,
            startTime = null,
            headers = {},
            userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        } = options;

        return new Promise((resolve, reject) => {
            const conversionId = Date.now().toString();

            try {
                // Ensure output directory exists
                const outputDir = path.dirname(outputPath);
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir, { recursive: true });
                }

                // Create FFmpeg command
                let command = ffmpeg(inputUrl);

                // Enhanced input options for M3U8/HLS streams
                const inputOptions = [
                    '-user_agent', userAgent,
                    '-headers', Object.entries(headers)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\r\n'),
                    // HLS-specific options
                    '-protocol_whitelist', 'file,http,https,tcp,tls,crypto',
                    '-allowed_extensions', 'ALL',
                    '-reconnect', '1',
                    '-reconnect_streamed', '1',
                    '-reconnect_delay_max', '5'
                ];

                command = command.inputOptions(inputOptions);

                // Enhanced output options for better M3U8 compatibility
                const baseOutputOptions = [
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-movflags', '+faststart',
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts',
                    '-max_muxing_queue_size', '1024'
                ];

                // Set quality-specific options
                if (quality === 'high') {
                    command = command.outputOptions([
                        ...baseOutputOptions,
                        '-preset', 'medium',
                        '-crf', '23',
                        '-profile:v', 'high',
                        '-level', '4.0'
                    ]);
                } else if (quality === 'medium') {
                    command = command.outputOptions([
                        ...baseOutputOptions,
                        '-preset', 'fast',
                        '-crf', '28',
                        '-profile:v', 'main',
                        '-level', '3.1'
                    ]);
                } else if (quality === 'low') {
                    command = command.outputOptions([
                        ...baseOutputOptions,
                        '-preset', 'ultrafast',
                        '-crf', '32',
                        '-profile:v', 'baseline',
                        '-level', '3.0'
                    ]);
                }

                // Set start time if specified
                if (startTime) {
                    command = command.seekInput(startTime);
                }

                // Set duration if specified
                if (maxDuration) {
                    command = command.duration(maxDuration);
                }

                // Store conversion info
                this.activeConversions.set(conversionId, {
                    inputUrl,
                    outputPath,
                    startTime: Date.now(),
                    command
                });

                // Set up progress tracking
                command.on('progress', (progress) => {
                    this.emit('progress', {
                        conversionId,
                        percent: progress.percent || 0,
                        currentTime: progress.timemark,
                        targetSize: progress.targetSize,
                        currentKbps: progress.currentKbps
                    });
                });

                // Handle conversion start
                command.on('start', (commandLine) => {
                    console.log('FFmpeg command:', commandLine);
                    this.emit('start', { conversionId, commandLine });
                });

                // Handle conversion completion
                command.on('end', () => {
                    this.activeConversions.delete(conversionId);
                    this.emit('complete', { conversionId, outputPath });
                    resolve(outputPath);
                });

                // Handle errors
                command.on('error', (err) => {
                    this.activeConversions.delete(conversionId);
                    this.emit('error', { conversionId, error: err.message });
                    reject(new Error(`Conversion failed: ${err.message}`));
                });

                // Start conversion
                command.save(outputPath);

            } catch (error) {
                reject(new Error(`Failed to start conversion: ${error.message}`));
            }
        });
    }

    /**
     * Get video information using FFprobe
     * @param {string} inputUrl - URL or path of the video
     * @returns {Promise<Object>} - Video information
     */
    async getVideoInfo(inputUrl) {
        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(inputUrl, (err, metadata) => {
                if (err) {
                    reject(new Error(`Failed to get video info: ${err.message}`));
                    return;
                }

                const videoStream = metadata.streams.find(s => s.codec_type === 'video');
                const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

                const info = {
                    duration: metadata.format.duration,
                    size: metadata.format.size,
                    bitrate: metadata.format.bit_rate,
                    format: metadata.format.format_name,
                    video: videoStream ? {
                        codec: videoStream.codec_name,
                        width: videoStream.width,
                        height: videoStream.height,
                        fps: eval(videoStream.r_frame_rate) || 0,
                        bitrate: videoStream.bit_rate
                    } : null,
                    audio: audioStream ? {
                        codec: audioStream.codec_name,
                        sampleRate: audioStream.sample_rate,
                        channels: audioStream.channels,
                        bitrate: audioStream.bit_rate
                    } : null
                };

                resolve(info);
            });
        });
    }

    /**
     * Cancel an active conversion
     * @param {string} conversionId - ID of the conversion to cancel
     */
    cancelConversion(conversionId) {
        const conversion = this.activeConversions.get(conversionId);
        if (conversion && conversion.command) {
            conversion.command.kill('SIGKILL');
            this.activeConversions.delete(conversionId);
            this.emit('cancelled', { conversionId });
        }
    }

    /**
     * Get list of active conversions
     * @returns {Array} - List of active conversion IDs
     */
    getActiveConversions() {
        return Array.from(this.activeConversions.keys());
    }

    /**
     * Check if a URL is a streaming format that can be converted
     * @param {string} url - URL to check
     * @returns {boolean} - True if it's a convertible streaming format
     */
    isConvertibleFormat(url) {
        const streamingPatterns = [
            /\.m3u8(\?.*)?$/i,
            /\.ts(\?.*)?$/i,
            /\/manifest\.m3u8/i,
            /\/playlist\.m3u8/i,
            /hls/i
        ];

        return streamingPatterns.some(pattern => pattern.test(url));
    }

    /**
     * Generate a safe filename from URL
     * @param {string} url - Source URL
     * @param {string} title - Optional title
     * @returns {string} - Safe filename
     */
    generateFilename(url, title = null) {
        let filename = title || 'video';

        // Clean up the filename
        filename = filename
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .substring(0, 100);

        // Add timestamp to make it unique
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        return `${filename}_${timestamp}.mp4`;
    }
}

module.exports = { VideoConverter };
