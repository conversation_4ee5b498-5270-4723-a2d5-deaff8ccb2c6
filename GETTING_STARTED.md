# Getting Started with VideoExtract 🚀

Welcome to VideoExtract! This guide will help you get up and running quickly.

## 📋 Prerequisites

Before you begin, make sure you have:

- **Node.js 16 or higher** - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn**
- **Windows 10/11** (for this build, but cross-platform compatible)

## 🛠️ Installation

1. **Open Terminal/Command Prompt** in the VideoExtract directory

2. **Install Dependencies**
   ```bash
   npm install
   ```
   
   This will:
   - Install all required Node.js packages
   - Download Play<PERSON>'s Chromium browser (~150MB)
   - Set up the development environment

3. **Verify Installation**
   ```bash
   npm test
   ```
   
   This runs a simple test to make sure everything is working.

## 🎯 Running the Application

### Quick Start
```bash
npm start
```

### Development Mode (with DevTools)
```bash
npm run dev
```

### Using the Dev Script
```bash
node dev-scripts.js start    # Normal mode
node dev-scripts.js dev      # Development mode
node dev-scripts.js test     # Run tests
```

## 🖥️ Using VideoExtract

1. **Launch the app** using one of the methods above
2. **Enter a URL** in the input field (e.g., `https://example.com/video-page`)
3. **Configure options** (optional):
   - **Wait Time**: How long to wait for videos to load (default: 5 seconds)
   - **Timeout**: Maximum extraction time (default: 30 seconds)
   - **Headless Mode**: Run browser in background (recommended)
4. **Click "Extract Videos"** and wait for results
5. **View results** with options to:
   - Copy video URLs to clipboard
   - Preview videos in the app
   - Open videos in your default browser

## 🎬 What VideoExtract Can Find

- **Direct Video Files**: MP4, WebM, AVI, MOV, MKV, FLV, WMV, M4V, 3GP, OGV
- **Streaming Formats**: M3U8 (HLS), MPD (DASH)
- **Embedded Videos**: From various video platforms
- **Dynamic Content**: Videos loaded via JavaScript

## 🔧 Advanced Configuration

### Adjusting Settings for Different Sites

**For slow-loading sites:**
- Increase wait time to 10-15 seconds
- Increase timeout to 60+ seconds

**For debugging:**
- Disable headless mode to see what's happening
- Use development mode to check console logs

**For sites with authentication:**
- The app runs in a clean browser session
- Some sites may require manual login (future feature)

## 📦 Building for Distribution

### Build for Current Platform
```bash
npm run build
```

### Build for Specific Platforms
```bash
npm run build-win     # Windows
npm run build-mac     # macOS  
npm run build-linux   # Linux
```

Built applications will be in the `dist/` folder.

## 🐛 Troubleshooting

### Common Issues

**"No videos found"**
- Try increasing the wait time
- Some sites load videos only after user interaction
- Check if the site actually has downloadable videos

**App won't start**
- Make sure Node.js 16+ is installed: `node --version`
- Reinstall dependencies: `rm -rf node_modules && npm install`
- Check for error messages in the terminal

**Extraction takes too long**
- Reduce timeout setting
- Some sites may have anti-bot protection
- Try with a different URL first

**Videos won't preview**
- Some video URLs are temporary or require authentication
- Try opening the URL directly in your browser
- Check your internet connection

### Getting Help

1. **Check the console** (F12 in development mode) for error messages
2. **Try the test script**: `npm test`
3. **Test with known working sites** like W3Schools examples
4. **Check your internet connection**

## 🔒 Privacy & Security

- **No data collection**: VideoExtract doesn't send any data anywhere
- **Local processing**: Everything happens on your computer
- **Temporary browser**: Uses a clean browser session each time
- **No storage**: Doesn't save any personal information

## 📚 Technical Details

### Architecture
- **Frontend**: HTML, CSS, JavaScript with modern UI
- **Backend**: Node.js with Electron framework
- **Browser Automation**: Playwright with Chromium
- **Security**: Context isolation and secure IPC communication

### File Structure
```
VideoExtract/
├── main.js              # Main Electron process
├── preload.js           # Secure communication bridge
├── index.html           # User interface
├── renderer.js          # Frontend logic
├── styles.css           # Modern styling
├── video-extractor.js   # Core extraction logic
├── package.json         # Project configuration
└── README.md           # Documentation
```

## 🤝 Contributing

Want to improve VideoExtract? Great!

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

VideoExtract is open source software licensed under the MIT License.

---

**Happy video extracting! 🎬✨**

If you encounter any issues or have suggestions, please don't hesitate to reach out!
