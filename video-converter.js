const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');
const ffprobeStatic = require('ffprobe-static');
const path = require('path');
const fs = require('fs');
const { EventEmitter } = require('events');

// Set FFmpeg and FFprobe paths
ffmpeg.setFfmpegPath(ffmpegStatic);
ffmpeg.setFfprobePath(ffprobeStatic.path);

class VideoConverter extends EventEmitter {
    constructor() {
        super();
        this.activeConversions = new Map();
    }

    /**
     * Convert M3U8 or TS files to MP4
     * @param {string} inputUrl - URL of the M3U8 or TS file
     * @param {string} outputPath - Path where to save the MP4 file
     * @param {Object} options - Conversion options
     * @returns {Promise<string>} - Path to the converted file
     */
    async convertToMp4(inputUrl, outputPath, options = {}) {
        const {
            quality = 'high',
            maxDuration = null,
            startTime = null,
            headers = {},
            userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        } = options;

        return new Promise((resolve, reject) => {
            const conversionId = Date.now().toString();
            
            try {
                // Ensure output directory exists
                const outputDir = path.dirname(outputPath);
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir, { recursive: true });
                }

                // Create FFmpeg command
                let command = ffmpeg(inputUrl);

                // Set user agent and headers for HTTP requests
                const inputOptions = [
                    '-user_agent', userAgent,
                    '-headers', Object.entries(headers)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\r\n')
                ];

                command = command.inputOptions(inputOptions);

                // Set quality options
                if (quality === 'high') {
                    command = command
                        .videoCodec('libx264')
                        .audioCodec('aac')
                        .outputOptions([
                            '-preset', 'medium',
                            '-crf', '23',
                            '-movflags', '+faststart'
                        ]);
                } else if (quality === 'medium') {
                    command = command
                        .videoCodec('libx264')
                        .audioCodec('aac')
                        .outputOptions([
                            '-preset', 'fast',
                            '-crf', '28',
                            '-movflags', '+faststart'
                        ]);
                } else if (quality === 'low') {
                    command = command
                        .videoCodec('libx264')
                        .audioCodec('aac')
                        .outputOptions([
                            '-preset', 'ultrafast',
                            '-crf', '32',
                            '-movflags', '+faststart'
                        ]);
                }

                // Set start time if specified
                if (startTime) {
                    command = command.seekInput(startTime);
                }

                // Set duration if specified
                if (maxDuration) {
                    command = command.duration(maxDuration);
                }

                // Store conversion info
                this.activeConversions.set(conversionId, {
                    inputUrl,
                    outputPath,
                    startTime: Date.now(),
                    command
                });

                // Set up progress tracking
                command.on('progress', (progress) => {
                    this.emit('progress', {
                        conversionId,
                        percent: progress.percent || 0,
                        currentTime: progress.timemark,
                        targetSize: progress.targetSize,
                        currentKbps: progress.currentKbps
                    });
                });

                // Handle conversion start
                command.on('start', (commandLine) => {
                    console.log('FFmpeg command:', commandLine);
                    this.emit('start', { conversionId, commandLine });
                });

                // Handle conversion completion
                command.on('end', () => {
                    this.activeConversions.delete(conversionId);
                    this.emit('complete', { conversionId, outputPath });
                    resolve(outputPath);
                });

                // Handle errors
                command.on('error', (err) => {
                    this.activeConversions.delete(conversionId);
                    this.emit('error', { conversionId, error: err.message });
                    reject(new Error(`Conversion failed: ${err.message}`));
                });

                // Start conversion
                command.save(outputPath);

            } catch (error) {
                reject(new Error(`Failed to start conversion: ${error.message}`));
            }
        });
    }

    /**
     * Get video information using FFprobe
     * @param {string} inputUrl - URL or path of the video
     * @returns {Promise<Object>} - Video information
     */
    async getVideoInfo(inputUrl) {
        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(inputUrl, (err, metadata) => {
                if (err) {
                    reject(new Error(`Failed to get video info: ${err.message}`));
                    return;
                }

                const videoStream = metadata.streams.find(s => s.codec_type === 'video');
                const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

                const info = {
                    duration: metadata.format.duration,
                    size: metadata.format.size,
                    bitrate: metadata.format.bit_rate,
                    format: metadata.format.format_name,
                    video: videoStream ? {
                        codec: videoStream.codec_name,
                        width: videoStream.width,
                        height: videoStream.height,
                        fps: eval(videoStream.r_frame_rate) || 0,
                        bitrate: videoStream.bit_rate
                    } : null,
                    audio: audioStream ? {
                        codec: audioStream.codec_name,
                        sampleRate: audioStream.sample_rate,
                        channels: audioStream.channels,
                        bitrate: audioStream.bit_rate
                    } : null
                };

                resolve(info);
            });
        });
    }

    /**
     * Cancel an active conversion
     * @param {string} conversionId - ID of the conversion to cancel
     */
    cancelConversion(conversionId) {
        const conversion = this.activeConversions.get(conversionId);
        if (conversion && conversion.command) {
            conversion.command.kill('SIGKILL');
            this.activeConversions.delete(conversionId);
            this.emit('cancelled', { conversionId });
        }
    }

    /**
     * Get list of active conversions
     * @returns {Array} - List of active conversion IDs
     */
    getActiveConversions() {
        return Array.from(this.activeConversions.keys());
    }

    /**
     * Check if a URL is a streaming format that can be converted
     * @param {string} url - URL to check
     * @returns {boolean} - True if it's a convertible streaming format
     */
    isConvertibleFormat(url) {
        const streamingPatterns = [
            /\.m3u8(\?.*)?$/i,
            /\.ts(\?.*)?$/i,
            /\/manifest\.m3u8/i,
            /\/playlist\.m3u8/i,
            /hls/i
        ];

        return streamingPatterns.some(pattern => pattern.test(url));
    }

    /**
     * Generate a safe filename from URL
     * @param {string} url - Source URL
     * @param {string} title - Optional title
     * @returns {string} - Safe filename
     */
    generateFilename(url, title = null) {
        let filename = title || 'video';
        
        // Clean up the filename
        filename = filename
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .substring(0, 100);

        // Add timestamp to make it unique
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        return `${filename}_${timestamp}.mp4`;
    }
}

module.exports = { VideoConverter };
