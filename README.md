# VideoExtract 🎬

A powerful Electron application that automatically extracts direct video URLs from any webpage using advanced web scraping techniques.

![VideoExtract Screenshot](screenshot.png)

## ✨ Features

- **🔍 Smart Detection**: Automatically finds video URLs from any webpage
- **🎯 Multiple Formats**: Supports MP4, WebM, M3U8, TS, and many other video formats
- **🔄 M3U8/TS to MP4 Conversion**: Convert streaming formats to downloadable MP4 files
- **📊 Real-time Progress**: Live conversion progress with cancel option
- **🖥️ Modern UI**: Clean, intuitive interface built with modern web technologies
- **⚡ Fast Extraction**: Uses Playwright for efficient web automation
- **📋 Easy Copy**: One-click copying of video URLs to clipboard
- **👁️ Preview**: Built-in video preview functionality
- **💾 Smart Downloads**: Automatic file naming and organized storage
- **🔧 Advanced Options**: Customizable timeout, wait time, and headless mode
- **🌐 Cross-Platform**: Works on Windows, macOS, and Linux

## 🚀 Quick Start

### Prerequisites

- Node.js 16 or higher
- npm or yarn package manager

### Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd VideoExtract
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Playwright browsers**
   ```bash
   npx playwright install chromium
   ```

4. **Run the application**
   ```bash
   npm start
   ```

## 🛠️ Development

### Running in Development Mode

```bash
npm run dev
```

This will start the application with developer tools enabled.

### Building for Production

```bash
# Build for current platform
npm run build

# Build for specific platforms
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

## 📖 How to Use

1. **Enter URL**: Paste the webpage URL containing the video you want to extract
2. **Configure Options** (optional):
   - Adjust wait time for slow-loading pages
   - Set timeout for maximum extraction time
   - Toggle headless mode for debugging
3. **Extract**: Click "Extract Videos" to start the process
4. **View Results**: Browse found videos with preview, copy, and open options
5. **Convert Videos** (for M3U8/TS):
   - Click "Convert to MP4" on convertible videos
   - Monitor real-time conversion progress
   - Files are automatically saved to Downloads/VideoExtract folder
   - Cancel conversions if needed

## 🔧 Advanced Options

- **Wait Time**: How long to wait for videos to load (1-30 seconds)
- **Timeout**: Maximum time for the entire extraction process (10-120 seconds)
- **Headless Mode**: Run browser in background (recommended for better performance)

## 🎯 Supported Video Formats

### Extraction
- **Direct Video Files**: MP4, WebM, AVI, MOV, MKV, FLV, WMV, M4V, 3GP, OGV
- **Streaming Formats**: M3U8 (HLS), MPD (DASH), TS segments
- **Platform-Specific**: YouTube, Vimeo, and other video platforms

### Conversion (M3U8/TS → MP4)
- **Input Formats**: M3U8 playlists, TS video segments, HLS streams
- **Output Format**: High-quality MP4 with H.264 video and AAC audio
- **Quality Options**: High, Medium, Low (configurable)
- **Features**: Fast start optimization, progress tracking, cancellation support

## 🔒 Privacy & Security

- **No Data Collection**: VideoExtract doesn't collect or store any personal data
- **Local Processing**: All video extraction happens locally on your machine
- **Secure**: Uses Electron's security best practices with context isolation

## 🐛 Troubleshooting

### Common Issues

**No videos found?**
- Increase the wait time for slow-loading pages
- Try disabling headless mode to see what's happening
- Some sites may require user interaction to load videos

**Application won't start?**
- Make sure Node.js 16+ is installed
- Run `npm install` to ensure all dependencies are installed
- Try `npx playwright install chromium` to reinstall browser

**Videos won't play in preview?**
- Some video URLs may be temporary or require authentication
- Try opening the URL directly in your browser
- Check if the video format is supported by your browser

### Getting Help

If you encounter issues:
1. Check the console for error messages (F12 in development mode)
2. Try with different websites to isolate the problem
3. Ensure your internet connection is stable

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Playwright** - For powerful web automation capabilities
- **Electron** - For cross-platform desktop app framework
- **The open-source community** - For inspiration and tools

## 📞 Support

For support, questions, or feature requests, please open an issue on the GitHub repository.

---

**⚠️ Disclaimer**: This tool is for educational and personal use only. Please respect website terms of service and copyright laws when extracting video content.
