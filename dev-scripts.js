#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

const commands = {
    start: () => {
        console.log('🚀 Starting VideoExtract...');
        spawn('npm', ['start'], { stdio: 'inherit', shell: true });
    },
    
    dev: () => {
        console.log('🔧 Starting VideoExtract in development mode...');
        spawn('npm', ['run', 'dev'], { stdio: 'inherit', shell: true });
    },
    
    test: () => {
        console.log('🧪 Running extraction test...');
        spawn('node', ['test-extraction.js'], { stdio: 'inherit', shell: true });
    },
    
    build: () => {
        console.log('📦 Building VideoExtract...');
        spawn('npm', ['run', 'build'], { stdio: 'inherit', shell: true });
    },
    
    help: () => {
        console.log(`
🎬 VideoExtract Development Scripts

Usage: node dev-scripts.js <command>

Commands:
  start    Start the application
  dev      Start in development mode (with DevTools)
  test     Run video extraction test
  build    Build the application for distribution
  help     Show this help message

Examples:
  node dev-scripts.js start
  node dev-scripts.js dev
  node dev-scripts.js test
        `);
    }
};

const command = process.argv[2];

if (!command || !commands[command]) {
    commands.help();
    process.exit(1);
}

commands[command]();
