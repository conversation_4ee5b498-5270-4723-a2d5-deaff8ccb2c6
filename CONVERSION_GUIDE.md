# 🔄 Video Conversion Guide

VideoExtract now includes powerful M3U8 and TS to MP4 conversion capabilities! This guide will help you understand and use these features effectively.

## 🎯 What Can Be Converted?

### ✅ Supported Input Formats
- **M3U8 Files**: HTTP Live Streaming (HLS) playlists
- **TS Segments**: Transport Stream video segments
- **HLS Streams**: Live and on-demand streaming content
- **Manifest Files**: Video streaming manifests

### ❌ Not Convertible
- **Direct MP4/WebM**: Already in downloadable format
- **DASH Streams**: Different streaming protocol (future feature)
- **Protected Content**: DRM-protected videos

## 🚀 How to Convert Videos

### Step 1: Extract Videos
1. Enter a webpage URL in VideoExtract
2. Click "Extract Videos" to find all video sources
3. Wait for the extraction to complete

### Step 2: Identify Convertible Videos
- Look for the green **"Convertible"** badge next to video information
- Videos with M3U8 or TS URLs will show a **"🔄 Convert to MP4"** button

### Step 3: Start Conversion
1. Click the **"🔄 Convert to MP4"** button
2. The conversion will start automatically
3. A progress bar will appear showing:
   - Conversion percentage
   - Current time being processed
   - Cancel option

### Step 4: Monitor Progress
- **Real-time updates**: Progress percentage and time
- **Cancel anytime**: Click "❌ Cancel" to stop conversion
- **Background processing**: Continue using the app while converting

### Step 5: Access Converted Files
- Files are automatically saved to: `Downloads/VideoExtract/`
- Notification appears when conversion completes
- Option to open the folder containing the converted file

## ⚙️ Conversion Settings

### Quality Options
- **High Quality** (Default)
  - H.264 video codec
  - AAC audio codec
  - CRF 23 (excellent quality)
  - Medium encoding preset

- **Medium Quality**
  - H.264 video codec
  - AAC audio codec
  - CRF 28 (good quality, smaller file)
  - Fast encoding preset

- **Low Quality**
  - H.264 video codec
  - AAC audio codec
  - CRF 32 (acceptable quality, small file)
  - Ultra-fast encoding preset

### Advanced Features
- **Fast Start**: MP4 files optimized for web playback
- **Progress Tracking**: Real-time conversion progress
- **Error Handling**: Automatic retry and error reporting
- **Cancellation**: Stop conversions at any time

## 📁 File Management

### Automatic Naming
Files are automatically named using this pattern:
```
{title}_{timestamp}.mp4
```

Examples:
- `My_Video_2024-06-14T10-30-00-000Z.mp4`
- `Live_Stream_2024-06-14T10-30-00-000Z.mp4`

### Storage Location
- **Windows**: `C:\Users\<USER>\Downloads\VideoExtract\`
- **macOS**: `/Users/<USER>/Downloads/VideoExtract/`
- **Linux**: `/home/<USER>/Downloads/VideoExtract/`

### File Organization
- All converted files are stored in the VideoExtract folder
- Unique timestamps prevent filename conflicts
- Easy to find and manage your converted videos

## 🔧 Technical Details

### Conversion Process
1. **Input Analysis**: FFprobe analyzes the source stream
2. **Stream Processing**: FFmpeg processes video/audio streams
3. **Encoding**: H.264/AAC encoding with optimized settings
4. **Output**: MP4 file with fast-start optimization

### Performance
- **CPU Usage**: Moderate during conversion
- **Memory Usage**: Low memory footprint
- **Speed**: Depends on video length and quality settings
- **Efficiency**: Optimized FFmpeg parameters for best results

### Requirements
- **FFmpeg**: Bundled with the application
- **Disk Space**: Ensure adequate space for converted files
- **Internet**: Required for downloading streaming content

## 🐛 Troubleshooting

### Common Issues

**"Conversion failed" error**
- Check internet connection
- Verify the M3U8 URL is still valid
- Try with a different quality setting
- Some streams may have geographic restrictions

**Slow conversion speed**
- Lower quality setting for faster conversion
- Close other resource-intensive applications
- Ensure adequate disk space

**"No videos found" for streaming sites**
- Increase wait time in advanced options
- Some sites require user interaction to load streams
- Try disabling headless mode to see what's happening

**Conversion stops unexpectedly**
- Check available disk space
- Verify internet connection stability
- Some streams may have time limits

### Getting Help

1. **Check the console**: Use development mode (F12) for detailed logs
2. **Test with known working streams**: Try with different M3U8 URLs
3. **Verify FFmpeg**: Run `npm run test-conversion` to check setup
4. **Check file permissions**: Ensure write access to Downloads folder

## 💡 Tips & Best Practices

### For Best Results
- **Stable Internet**: Ensure reliable connection during conversion
- **Adequate Space**: Check available disk space before converting
- **Quality vs Speed**: Higher quality = slower conversion but better output
- **Batch Processing**: Convert multiple videos one at a time for stability

### Performance Optimization
- **Close Unnecessary Apps**: Free up system resources
- **Use High Quality**: For archival purposes
- **Use Low Quality**: For quick previews or limited storage
- **Monitor Progress**: Keep an eye on conversion progress

### Legal Considerations
- **Respect Copyright**: Only convert content you have rights to use
- **Terms of Service**: Check website terms before extracting/converting
- **Personal Use**: Conversions should be for personal use only
- **Fair Use**: Understand fair use guidelines in your jurisdiction

## 🔮 Future Features

Planned enhancements for video conversion:
- **DASH Stream Support**: Convert DASH streams to MP4
- **Batch Conversion**: Convert multiple videos simultaneously
- **Custom Quality Settings**: Fine-tune encoding parameters
- **Format Options**: Support for other output formats (WebM, AVI)
- **Subtitle Extraction**: Extract and embed subtitles
- **Audio-Only Conversion**: Extract audio tracks to MP3/AAC

---

**Happy converting! 🎬✨**

For more help, check the main README.md or open an issue on the project repository.
