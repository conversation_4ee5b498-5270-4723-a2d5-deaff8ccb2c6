// Test script for M3U8 analyzer functionality
const { M3U8Analyzer } = require('./m3u8-analyzer');

async function testM3U8Analyzer() {
    console.log('🧪 Testing M3U8 Analyzer...\n');
    
    const analyzer = new M3U8Analyzer();
    
    // Test M3U8 content parsing
    console.log('📋 Testing M3U8 content parsing:');
    
    // Sample master playlist content
    const masterPlaylistContent = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-STREAM-INF:BANDWIDTH=1280000,RESOLUTION=854x480
480p.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=2560000,RESOLUTION=1280x720
720p.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=5120000,RESOLUTION=1920x1080
1080p.m3u8`;

    const masterParsed = analyzer.parseM3U8Content(masterPlaylistContent, 'https://example.com/');
    console.log('   Master playlist:', {
        isMaster: masterParsed.isMasterPlaylist,
        variants: masterParsed.variants.length,
        qualities: masterParsed.variants.map(v => v.quality)
    });

    // Sample media playlist content
    const mediaPlaylistContent = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-PLAYLIST-TYPE:VOD
#EXTINF:9.009,
segment001.ts
#EXTINF:9.009,
segment002.ts
#EXTINF:9.009,
segment003.ts
#EXTINF:5.005,
segment004.ts
#EXT-X-ENDLIST`;

    const mediaParsed = analyzer.parseM3U8Content(mediaPlaylistContent, 'https://example.com/');
    console.log('   Media playlist:', {
        isMedia: mediaParsed.isMediaPlaylist,
        segments: mediaParsed.segments.length,
        duration: mediaParsed.totalDuration,
        isVOD: mediaParsed.isVOD
    });

    // Test URL resolution
    console.log('\n🔗 Testing URL resolution:');
    const testUrls = [
        { url: 'segment.ts', base: 'https://example.com/videos/', expected: 'https://example.com/videos/segment.ts' },
        { url: '../other/segment.ts', base: 'https://example.com/videos/hd/', expected: 'https://example.com/videos/other/segment.ts' },
        { url: 'https://cdn.example.com/segment.ts', base: 'https://example.com/', expected: 'https://cdn.example.com/segment.ts' }
    ];

    testUrls.forEach(test => {
        const resolved = analyzer.resolveUrl(test.url, test.base);
        const status = resolved === test.expected ? '✅' : '❌';
        console.log(`   ${status} ${test.url} + ${test.base} = ${resolved}`);
    });

    // Test quality detection
    console.log('\n🎯 Testing quality detection:');
    const resolutions = [360, 480, 720, 1080, 1440, 2160];
    resolutions.forEach(height => {
        const quality = analyzer.getQualityLabel(height);
        console.log(`   ${height}p -> ${quality}`);
    });

    // Test size estimation
    console.log('\n💾 Testing size estimation:');
    const testDurations = [60, 300, 1800, 3600, 7200]; // 1min, 5min, 30min, 1hr, 2hr
    testDurations.forEach(duration => {
        const mockParsed = {
            segments: [{ duration }],
            totalDuration: duration
        };
        const estimated = analyzer.estimateSize(mockParsed);
        console.log(`   ${duration}s (${Math.round(duration/60)}min) -> ${estimated}`);
    });

    console.log('\n✅ M3U8 Analyzer tests completed!');
    return true;
}

// Test with real M3U8 URLs (if available)
async function testRealM3U8() {
    console.log('\n🌐 Testing with sample M3U8 URLs...');
    
    const analyzer = new M3U8Analyzer();
    
    // These are example URLs - replace with real ones for testing
    const testUrls = [
        // Apple's sample HLS streams
        'https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8',
        // Add more test URLs here
    ];

    for (const url of testUrls) {
        try {
            console.log(`\n🔍 Analyzing: ${url}`);
            const analysis = await analyzer.analyzeM3U8(url);
            const summary = analyzer.getSummary(analysis);
            
            console.log('   Results:', {
                type: summary.type,
                downloadable: summary.downloadable,
                duration: summary.totalDuration ? `${Math.round(summary.totalDuration)}s` : 'Unknown',
                segments: summary.totalSegments || 'Unknown',
                size: summary.estimatedSize || 'Unknown'
            });
            
            if (analysis.type === 'master') {
                console.log('   Variants:', analysis.variants.map(v => 
                    `${v.quality || 'Unknown'} (${v.bandwidth || 'Unknown'} bps)`
                ).join(', '));
            }
            
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
        }
    }
}

// Test M3U8 detection patterns
function testM3U8Detection() {
    console.log('\n🔍 Testing M3U8 detection patterns:');
    
    const testUrls = [
        'https://example.com/video.m3u8',
        'https://example.com/playlist.m3u8?token=abc123',
        'https://example.com/manifest.m3u8',
        'https://example.com/stream/index.m3u8',
        'https://example.com/hls/master.m3u8',
        'https://example.com/video.mp4',
        'https://example.com/stream.ts',
        'https://example.com/segment001.ts'
    ];

    const analyzer = new M3U8Analyzer();
    
    testUrls.forEach(url => {
        // Simulate the detection logic from video-converter.js
        const isM3U8 = url.includes('.m3u8');
        const isTS = url.includes('.ts');
        const status = isM3U8 ? '📋 M3U8' : isTS ? '📦 TS' : '❌ Other';
        console.log(`   ${status} ${url}`);
    });
}

// Run all tests
async function runAllTests() {
    console.log('🎯 M3U8 Analyzer Test Suite\n');
    console.log('=' .repeat(50));
    
    try {
        // Test basic functionality
        await testM3U8Analyzer();
        
        console.log('\n' + '=' .repeat(50));
        
        // Test detection patterns
        testM3U8Detection();
        
        console.log('\n' + '=' .repeat(50));
        
        // Test with real URLs (optional)
        await testRealM3U8();
        
        console.log('\n' + '=' .repeat(50));
        console.log('🎉 All M3U8 analyzer tests completed!');
        
        console.log('\n💡 Key Features:');
        console.log('   • Distinguishes master vs media playlists');
        console.log('   • Finds best quality streams automatically');
        console.log('   • Counts total segments for complete downloads');
        console.log('   • Estimates file sizes and durations');
        console.log('   • Detects live vs VOD content');
        console.log('   • Resolves relative URLs correctly');
        
    } catch (error) {
        console.error('💥 Test suite crashed:', error);
    }
}

// Run if called directly
if (require.main === module) {
    runAllTests().then(() => {
        console.log('\n🏁 Tests completed!');
        process.exit(0);
    }).catch(error => {
        console.error('💥 Tests crashed:', error);
        process.exit(1);
    });
}

module.exports = { testM3U8Analyzer, testRealM3U8, runAllTests };
