const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  extractVideo: (url, options) => ipcRenderer.invoke('extract-video', url, options),
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

  // Video conversion functions
  convertVideo: (inputUrl, options) => ipcRenderer.invoke('convert-video', inputUrl, options),
  getVideoInfo: (inputUrl) => ipcRenderer.invoke('get-video-info', inputUrl),
  cancelConversion: (conversionId) => ipcRenderer.invoke('cancel-conversion', conversionId),
  isConvertibleFormat: (url) => ipcRenderer.invoke('is-convertible-format', url),
  chooseDownloadLocation: (defaultFilename) => ipcRenderer.invoke('choose-download-location', defaultFilename),

  // Event listeners for conversion progress
  onConversionProgress: (callback) => ipcRenderer.on('conversion-progress', callback),
  onConversionComplete: (callback) => ipcRenderer.on('conversion-complete', callback),
  onConversionError: (callback) => ipcRenderer.on('conversion-error', callback),

  // Remove event listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Expose version info
contextBridge.exposeInMainWorld('versions', {
  node: () => process.versions.node,
  chrome: () => process.versions.chrome,
  electron: () => process.versions.electron
});
